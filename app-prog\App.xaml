<Application x:Class="NetworkManagement.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:NetworkManagement.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Converters -->
                    <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                    <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
                    <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
                    <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
                    <converters:ZeroToVisibilityConverter x:Key="ZeroToVisibilityConverter"/>
                    <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
                    <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
                    <converters:BoolToStatusColorConverter x:Key="BoolToStatusColorConverter"/>
                    <converters:BoolToStatusTextConverter x:Key="BoolToStatusTextConverter"/>

                    <!-- Permission Converters -->
                    <converters:CanEditDataConverter x:Key="CanEditDataConverter"/>
                    <converters:CanDeleteDataConverter x:Key="CanDeleteDataConverter"/>
                    <converters:CanViewDataConverter x:Key="CanViewDataConverter"/>
                    <converters:PermissionConverter x:Key="PermissionConverter"/>
                    <converters:CanEditUserConverter x:Key="CanEditUserConverter"/>
                    <converters:CanDeleteUserConverter x:Key="CanDeleteUserConverter"/>
                    <converters:BoolToToggleTextConverter x:Key="BoolToToggleTextConverter"/>

                    <!-- Column Visibility Converters -->
                    <converters:ColumnVisibilityConverter x:Key="ColumnVisibilityConverter"/>
                    <converters:SimpleColumnVisibilityConverter x:Key="SimpleColumnVisibilityConverter"/>

                    <Style x:Key="PageHeaderStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="#2196F3"/>
                        <Setter Property="Margin" Value="0,0,0,20"/>
                    </Style>

                    <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="10"/>
                        <Setter Property="Padding" Value="20"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>

                    <Style x:Key="MenuButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Height" Value="50"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
