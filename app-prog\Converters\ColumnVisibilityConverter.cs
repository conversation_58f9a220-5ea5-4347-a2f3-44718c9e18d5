using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using NetworkManagement.Models;

namespace NetworkManagement.Converters
{
    /// <summary>
    /// محول لتحديد رؤية الأعمدة بناءً على إعدادات المستخدم
    /// </summary>
    public class ColumnVisibilityConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length < 2)
                return Visibility.Visible;

            // القيمة الأولى: مجموعة إعدادات الأعمدة
            if (!(values[0] is ObservableCollection<ColumnSetting> columnSettings))
                return Visibility.Visible;

            // القيمة الثانية: اسم العمود
            if (!(values[1] is string columnName) || string.IsNullOrEmpty(columnName))
                return Visibility.Visible;

            // البحث عن إعداد العمود المحدد
            var setting = columnSettings.FirstOrDefault(c => c.ColumnName == columnName);
            
            // إذا لم يتم العثور على الإعداد أو كان مرئياً، إرجاع Visible
            if (setting == null || setting.IsVisible)
                return Visibility.Visible;

            // إذا كان العمود مطلوباً، إرجاع Visible حتى لو كان مخفياً
            if (setting.IsRequired)
                return Visibility.Visible;

            // إخفاء العمود
            return Visibility.Collapsed;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول مبسط لتحديد رؤية الأعمدة بناءً على اسم العمود فقط
    /// </summary>
    public class SimpleColumnVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (!(value is ObservableCollection<ColumnSetting> columnSettings))
                return Visibility.Visible;

            if (!(parameter is string columnName) || string.IsNullOrEmpty(columnName))
                return Visibility.Visible;

            var setting = columnSettings.FirstOrDefault(c => c.ColumnName == columnName);
            
            if (setting == null || setting.IsVisible || setting.IsRequired)
                return Visibility.Visible;

            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
