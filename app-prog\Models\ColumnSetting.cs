using CommunityToolkit.Mvvm.ComponentModel;

namespace NetworkManagement.Models
{
    /// <summary>
    /// نموذج إعدادات عمود في DataGrid
    /// </summary>
    public partial class ColumnSetting : ObservableObject
    {
        [ObservableProperty]
        private string columnName = string.Empty;

        [ObservableProperty]
        private string displayName = string.Empty;

        [ObservableProperty]
        private bool isVisible = true;

        [ObservableProperty]
        private bool isRequired = false;

        public ColumnSetting()
        {
        }

        public ColumnSetting(string columnName, string displayName, bool isVisible = true, bool isRequired = false)
        {
            ColumnName = columnName;
            DisplayName = displayName;
            IsVisible = isVisible;
            IsRequired = isRequired;
        }
    }
}
